import { ArrayRenderStrategy } from "./RenderStrategy";
import { CustomGridContainer } from "../components/CustomGridContainer";
import { CustomGridItem } from "../components/CustomGridItem";
import { Url } from "../../model/Urls";
import { useReportActions, useReportMode } from "../../context/ReportContext";
import { CustomReadOnlyInputField } from "../components/CustomReadOnlyInput";
import { renderSourceTooltip } from "./helpers.strategy";

export function useRenderOutrasUrls(sectionTitle: string): ArrayRenderStrategy<Url> {
  const actions = useReportActions();
  const mode = useReportMode();

  const shouldInclude = (isDeleted: boolean) => {
    switch (mode) {
      case "trash":
        return isDeleted;
      case "print-pdf":
      case undefined:
      default:
        return !isDeleted;
    }
  };

  const testEntryDeleted = (entry: any): boolean => {
    return entry.detalhes
      ? entry.detalhes.every((detalhe: any) =>
        (detalhe.value?.url?.is_deleted === true && detalhe.value?.dominio?.is_deleted === true) ||
        detalhe.is_deleted === true
      )
      : false;
  };

  const testSectionDeleted = (section: any): boolean =>
    Array.isArray(section.data) && section.data.every(testEntryDeleted);

  const calculateDataCount = (section: any): number => {
    if (!Array.isArray(section.data)) return 0;

    return section.data.reduce((count: number, entry: any) => {
      if (!entry.detalhes) return count;

      // contar itens não deletados (cada objeto de URL)
      const nonDeletedDetalhes = entry.detalhes.filter(
        (detalhe: any) =>
          !(detalhe.value?.url?.is_deleted === true && detalhe.value?.dominio?.is_deleted === true) &&
          detalhe.is_deleted !== true
      ).length;

      return count + nonDeletedDetalhes;
    }, 0);
  };

  const formatByKey: Record<
    string,
    (urls?: Url) => React.ReactElement | null
  > = {
    detalhes: (urls) => {
      if (!urls?.detalhes?.length) return null;

      const filteredDetalhes = urls.detalhes.filter((detalhe) => {
        const urlDeleted = detalhe.value?.url?.is_deleted === true;
        const dominioDeleted = detalhe.value?.dominio?.is_deleted === true;
        const detalheDeleted = detalhe.is_deleted === true;
        const allFieldsDeleted = urlDeleted && dominioDeleted;
        return shouldInclude(allFieldsDeleted || detalheDeleted);
      });

      if (filteredDetalhes.length === 0) return null;

      const onToggleField = (detalheIndex: number, fieldKey: string) => {
        const updater = actions.updateSectionEntries;
        if (!updater) return;

        updater(
          sectionTitle,
          entry => {
            const detalhe = (entry as any).detalhes?.[detalheIndex];
            if (detalhe?.value?.[fieldKey]) {
              detalhe.value[fieldKey].is_deleted = !detalhe.value[fieldKey].is_deleted;
            }
          },
          testEntryDeleted,
          testSectionDeleted,
          calculateDataCount
        );
      };

      return (
        <CustomGridContainer cols={2}>
          {filteredDetalhes.map((detalhe) => {
            const originalIndex = urls.detalhes.indexOf(detalhe);

            return (
              <CustomGridItem
                key={`url-detalhe-column-${originalIndex}`}
                cols={1}
              >
                <div className="">
                  {Object.entries(detalhe.value).map(([key, valueObj]) => {
                    const fieldDeleted = valueObj?.is_deleted === true;

                    // Só mostra o campo se ele deve ser incluído baseado no modo
                    if (!shouldInclude(fieldDeleted)) return null;

                    return (
                      <CustomGridItem
                        key={`detalhe-${originalIndex}-${key}`}
                        cols={1}
                        className="py-2 group"
                        onToggleField={() => onToggleField(originalIndex, key)}
                      >
                        <CustomReadOnlyInputField
                          label={`${(detalhe.label || key).toUpperCase()} ${originalIndex + 1}`}
                          value={String(
                            typeof valueObj === 'object' && valueObj?.value
                              ? valueObj.value
                              : valueObj
                          )}
                          tooltip={renderSourceTooltip(valueObj.source)}
                        />
                      </CustomGridItem>
                    );
                  })}
                </div>
              </CustomGridItem>
            );
          })}
        </CustomGridContainer>
      );
    },
  };

  const validateKeys = (keys: Array<keyof Url>): boolean => {
    return keys.some((campo) => campo in formatByKey);
  };

  const renderSingleItem = (urls: Url): React.ReactElement[] => {
    const keys = Object.keys(urls) as Array<keyof Url>;

    if (!validateKeys(keys)) {
      console.warn("[Seção Outras URLs] Chaves inválidas:", keys);
    }

    return keys
      .map((chave) => formatByKey[chave]?.(urls))
      .filter((el): el is React.ReactElement => el !== null);
  };

  const render = (dataArray: Url[]): React.ReactElement[] => {
    if (!Array.isArray(dataArray)) {
      console.warn("[Seção Outras URLs] Expected array but received:", typeof dataArray);
      return [];
    }

    const filteredData = dataArray.filter((entry) => {
      if (mode === "trash") {
        return entry.detalhes && entry.detalhes.some((detalhe: any) =>
          detalhe.value?.url?.is_deleted === true ||
          detalhe.value?.dominio?.is_deleted === true ||
          detalhe.is_deleted === true
        );
      } else {
        const isDeleted = testEntryDeleted(entry);
        return !isDeleted;
      }
    });

    if (filteredData.length === 0) {
      return [];
    }

    const allElements: React.ReactElement[] = [];

    filteredData.forEach((urls, index) => {
      const elements = renderSingleItem(urls);

      if (filteredData.length > 1) {
        allElements.push(
          <div key={`outras-urls-${index}`} className="mb-4">
            {elements}
          </div>
        );
      } else {
        allElements.push(...elements);
      }
    });

    return allElements;
  };

  const deleteSectionEntries = () => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;

    // No modo lixeira, restaura (is_deleted = false)
    // No modo normal, deleta (is_deleted = true)
    const targetDeletedState = mode !== "trash";

    updater(
      sectionTitle,
      entry => {
        // Percorre todos os detalhes e marca todos os campos como deletados/restaurados
        if (entry.detalhes) {
          entry.detalhes.forEach((detalhe: any) => {
            if (detalhe?.value) {
              Object.keys(detalhe.value).forEach(fieldKey => {
                if (detalhe.value[fieldKey]) {
                  detalhe.value[fieldKey].is_deleted = targetDeletedState;
                }
              });
            }
          });
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  return {
    validateKeys,
    formatByKey,
    render,
    testEntryDeleted,
    testSectionDeleted,
    deleteSectionEntries,
  };
}
