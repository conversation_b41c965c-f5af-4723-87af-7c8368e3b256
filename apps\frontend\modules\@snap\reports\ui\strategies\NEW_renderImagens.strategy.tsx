import React from "react";
import { ArrayRenderStrategy } from "./RenderStrategy";
import { CustomGridContainer } from "../components/CustomGridContainer";
import { CustomGridItem } from "../components/CustomGridItem";
import { Imagem } from "../../model/Imagens";
import { GridItem, CustomLabel } from "@snap/design-system";
import { useReportActions, useReportMode } from "../../context/ReportContext";
import { CustomReadOnlyInputField } from "../components/CustomReadOnlyInput";
import { renderSourceTooltip } from "./helpers.strategy";
import { ValidatedImage } from "../components/ValidateImage";
import { MdOutlineSubdirectoryArrowRight } from "react-icons/md";

export function useRenderImagens(sectionTitle: string): ArrayRenderStrategy<Imagem> {
  const actions = useReportActions();
  const mode = useReportMode();
  const isTrash = mode === "trash";

  const shouldIncludeBlock = (detalhe: any) => {
    const urlValue = detalhe.value?.url;
    return isTrash
      ? urlValue?.is_deleted === true
      : urlValue?.is_deleted === false;
  };

  const onToggleField = (blockIdx: number) => {
    actions.updateSectionEntries!(
      sectionTitle,
      (entry) => {
        const detalhe = (entry as any).detalhes?.[blockIdx];
        if (detalhe?.value?.url) {
          detalhe.value.url.is_deleted = !detalhe.value.url.is_deleted;
          // Para imagens, o bloco é deletado quando a URL é deletada
          detalhe.is_deleted = detalhe.value.url.is_deleted;
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  const onToggleBlock = (blockIdx: number) => {
    actions.updateSectionEntries!(
      sectionTitle,
      (entry) => {
        const detalhe = (entry as any).detalhes?.[blockIdx];
        if (detalhe?.value?.url) {
          // Determina o novo estado baseado no modo atual
          const targetDeletedState = isTrash ? false : true;

          // Define o is_deleted do bloco principal e da URL
          detalhe.is_deleted = targetDeletedState;
          detalhe.value.url.is_deleted = targetDeletedState;
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  const testEntryDeleted = (entry: any): boolean =>
    entry.detalhes?.every((d: any) =>
      d.value?.url?.is_deleted === true
    ) ?? false;

  const testSectionDeleted = (section: any): boolean =>
    Array.isArray(section.data) &&
    section.data.every(testEntryDeleted);

  const calculateDataCount = (section: any): number => {
    if (!Array.isArray(section.data)) return 0;

    return section.data.reduce((count: number, entry: any) => {
      if (!entry.detalhes) return count;

      // Count non-deleted blocks in detalhes array
      const nonDeletedBlocks = entry.detalhes.filter(
        (bloco: any) => bloco.is_deleted !== true
      ).length;

      return count + nonDeletedBlocks;
    }, 0);
  };

  const formatByKey: Record<
    string,
    (imagens?: Imagem) => React.ReactElement | null
  > = {
    detalhes: (imagens) => {
      if (!imagens?.detalhes?.length) return null;

      const filteredDetalhes = imagens.detalhes.filter((detalhe) => {
        const urlDeleted = detalhe.value?.url?.is_deleted === true;
        const detalheDeleted = detalhe.is_deleted === true;
        return shouldInclude(urlDeleted || detalheDeleted);
      });

      if (filteredDetalhes.length === 0) return null;

      const onToggleDetalhe = (detalheIndex: number) => {
        const updater = actions.updateSectionEntries;
        if (!updater) return;

        updater(
          sectionTitle,
          entry => {
            const detalhe = (entry as any).detalhes?.[detalheIndex];
            if (detalhe?.value?.url) {
              detalhe.value.url.is_deleted = !detalhe.value.url.is_deleted;
            }
          },
          testEntryDeleted,
          testSectionDeleted,
          calculateDataCount
        );
      };

      return (
        <CustomGridContainer cols={4}>
          {filteredDetalhes.map((detalhe, index) => {
            const originalIndex = imagens.detalhes.indexOf(detalhe);
            const urlValue = detalhe.value?.url?.value;
            const isUrlDeleted = detalhe.value?.url?.is_deleted === true;

            return (
              <CustomGridItem
                key={`imagem-detalhe-column-${originalIndex}`}
                cols={1}
                onToggleField={() => onToggleDetalhe(originalIndex)}
              >
                <div className="py-2 group">
                  <CustomReadOnlyInputField
                    label={`${(detalhe.label || 'URL').toUpperCase()} ${originalIndex + 1}`}
                    value={String(urlValue || '')}
                    tooltip={renderSourceTooltip(detalhe.value?.url?.source)}
                  />
                  {/* Renderiza a imagem seguindo a mesma lógica do campo URL */}
                  {shouldInclude(isUrlDeleted) && urlValue && (
                    <ValidatedImage
                      src={String(urlValue)}
                      alt={`Imagem ${originalIndex + 1}`}
                      className="w-full max-w-full h-60 mx-auto mt-2 bg-background/40 rounded-sm"
                    />
                  )}
                </div>
              </CustomGridItem>
            );
          })}
        </CustomGridContainer>
      );
    },
  };

  const validateKeys = (keys: Array<keyof Imagem>): boolean => {
    return keys.some((campo) => campo in formatByKey);
  };

  const renderSingleItem = (imagens: Imagem): React.ReactElement[] => {
    const keys = Object.keys(imagens) as Array<keyof Imagem>;

    if (!validateKeys(keys)) {
      console.warn("[Seção Imagens] Chaves inválidas:", keys);
    }

    return keys
      .map((chave) => formatByKey[chave]?.(imagens))
      .filter((el): el is React.ReactElement => el !== null);
  };

  const render = (dataArray: Imagem[]): React.ReactElement[] => {
    if (!Array.isArray(dataArray)) {
      console.warn("[Seção Imagens] Expected array but received:", typeof dataArray);
      return [];
    }

    const filteredData = dataArray.filter((entry) => {
      if (mode === "trash") {
        return entry.detalhes && entry.detalhes.some((detalhe: any) =>
          detalhe.value?.url?.is_deleted === true || detalhe.is_deleted === true
        );
      } else {
        const isDeleted = testEntryDeleted(entry);
        return !isDeleted;
      }
    });

    if (filteredData.length === 0) {
      return [];
    }

    const allElements: React.ReactElement[] = [];

    filteredData.forEach((imagens, index) => {
      const elements = renderSingleItem(imagens);

      if (filteredData.length > 1) {
        allElements.push(
          <div key={`imagens-${index}`} className="mb-4">
            {elements}
          </div>
        );
      } else {
        allElements.push(...elements);
      }
    });

    return allElements;
  };

  const deleteSectionEntries = () => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;

    // No modo lixeira, restaura (is_deleted = false)
    // No modo normal, deleta (is_deleted = true)
    const targetDeletedState = mode !== "trash";

    updater(
      sectionTitle,
      entry => {
        // Percorre todos os detalhes e marca a URL como deletada/restaurada
        if (entry.detalhes) {
          entry.detalhes.forEach((detalhe: any) => {
            if (detalhe?.value?.url) {
              detalhe.value.url.is_deleted = targetDeletedState;
            }
          });
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  return {
    validateKeys,
    formatByKey,
    render,
    testEntryDeleted,
    testSectionDeleted,
    deleteSectionEntries,
  };
}
